/**
 * 财务合并系统 Mock API 服务
 *
 * 模拟真实后端API的行为，包括：
 * - 网络延迟模拟
 * - 错误处理模拟
 * - 真实感数据生成
 */

// 模拟企业集团的组织架构数据
const mockOrganizationData = [
  {
    id: '1',
    code: 'TECH_GROUP',
    name: '科技集团有限公司',
    type: 'GROUP',
    children: [
      {
        id: '2',
        code: 'TECH_MAIN',
        name: '科技股份有限公司',
        type: 'COMPANY',
        parentId: '1',
        children: [
          {
            id: '5',
            code: 'TECH_RD',
            name: '技术研发中心',
            type: 'SUBSIDIARY',
            parentId: '2',
          },
          {
            id: '6',
            code: 'TECH_SALES',
            name: '销售服务公司',
            type: 'SUBSIDIARY',
            parentId: '2',
          },
        ],
      },
      {
        id: '3',
        code: 'TECH_HK',
        name: '科技(香港)有限公司',
        type: 'SUBSIDIARY',
        parentId: '1',
        children: [
          {
            id: '7',
            code: 'TECH_HK_TRADE',
            name: '香港贸易公司',
            type: 'SUBSIDIARY',
            parentId: '3',
          },
        ],
      },
      {
        id: '4',
        code: 'TECH_US',
        name: 'Technology (US) Inc.',
        type: 'SUBSIDIARY',
        parentId: '1',
      },
    ],
  },
];

// 模拟合并规则数据
const mockConsolidationRules = {
  2: {
    ownershipPercentage: 100,
    votingPercentage: 100,
    consolidationMethod: 'FULL',
    effectiveDate: '2020-01-01',
  },
  3: {
    ownershipPercentage: 75,
    votingPercentage: 75,
    consolidationMethod: 'PROPORTIONAL',
    effectiveDate: '2021-06-01',
  },
  4: {
    ownershipPercentage: 60,
    votingPercentage: 60,
    consolidationMethod: 'EQUITY',
    effectiveDate: '2022-03-01',
  },
  5: {
    ownershipPercentage: 100,
    votingPercentage: 100,
    consolidationMethod: 'FULL',
    effectiveDate: '2020-01-01',
  },
  6: {
    ownershipPercentage: 100,
    votingPercentage: 100,
    consolidationMethod: 'FULL',
    effectiveDate: '2020-01-01',
  },
  7: {
    ownershipPercentage: 100,
    votingPercentage: 100,
    consolidationMethod: 'FULL',
    effectiveDate: '2021-06-01',
  },
};

// 模拟会计科目数据
const mockChartOfAccounts = [
  { code: '1001', name: '库存现金', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1002', name: '银行存款', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1012', name: '其他货币资金', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1101', name: '应收票据', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1122', name: '应收账款', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1131', name: '应收股利', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1132', name: '应收利息', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1221', name: '其他应收款', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1401', name: '材料采购', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1402', name: '在途物资', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1403', name: '原材料', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1501', name: '持有至到期投资', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1701', name: '长期股权投资', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1801', name: '固定资产', type: 'ASSET', balanceDirection: 'DEBIT' },
  { code: '1802', name: '累计折旧', type: 'ASSET', balanceDirection: 'CREDIT' },

  // 负债类
  { code: '2001', name: '短期借款', type: 'LIABILITY', balanceDirection: 'CREDIT' },
  { code: '2101', name: '应付票据', type: 'LIABILITY', balanceDirection: 'CREDIT' },
  { code: '2121', name: '应付账款', type: 'LIABILITY', balanceDirection: 'CREDIT' },
  { code: '2201', name: '应付职工薪酬', type: 'LIABILITY', balanceDirection: 'CREDIT' },
  { code: '2211', name: '应交税费', type: 'LIABILITY', balanceDirection: 'CREDIT' },
  { code: '2501', name: '长期借款', type: 'LIABILITY', balanceDirection: 'CREDIT' },

  // 所有者权益类
  { code: '3001', name: '实收资本', type: 'EQUITY', balanceDirection: 'CREDIT' },
  { code: '3002', name: '资本公积', type: 'EQUITY', balanceDirection: 'CREDIT' },
  { code: '3003', name: '盈余公积', type: 'EQUITY', balanceDirection: 'CREDIT' },
  { code: '3004', name: '未分配利润', type: 'EQUITY', balanceDirection: 'CREDIT' },

  // 收入类
  { code: '4001', name: '主营业务收入', type: 'REVENUE', balanceDirection: 'CREDIT' },
  { code: '4002', name: '其他业务收入', type: 'REVENUE', balanceDirection: 'CREDIT' },

  // 费用类
  { code: '5001', name: '主营业务成本', type: 'EXPENSE', balanceDirection: 'DEBIT' },
  { code: '5101', name: '销售费用', type: 'EXPENSE', balanceDirection: 'DEBIT' },
  { code: '5201', name: '管理费用', type: 'EXPENSE', balanceDirection: 'DEBIT' },
  { code: '5301', name: '财务费用', type: 'EXPENSE', balanceDirection: 'DEBIT' },
];

// 生成随机财务数据
const generateRandomFinancialData = (organizationId, period, accountCodes) => {
  return accountCodes.map(account => ({
    id: `${organizationId}-${account.code}-${period}`,
    organizationId,
    accountCode: account.code,
    accountName: account.name,
    period,
    localAmount: Math.round((Math.random() - 0.5) * ******** * 100) / 100, // -5M到5M之间
    functionalAmount: Math.round((Math.random() - 0.5) * ******** * 100) / 100,
    groupAmount: Math.round((Math.random() - 0.5) * ******** * 100) / 100,
    localCurrency: 'CNY',
    functionalCurrency: 'CNY',
    groupCurrency: 'CNY',
    dataSource: 'MANUAL',
    status: Math.random() > 0.3 ? 'APPROVED' : 'DRAFT',
    createdAt: new Date().toISOString(),
  }));
};

class LucaNetMockApiService {
  constructor() {
    this.baseDelay = 500; // 基础延迟500ms
    this.errorRate = 0.05; // 5%错误率
  }

  // 模拟网络延迟
  async delay(ms) {
    const actualDelay = ms + Math.random() * ms; // 添加随机性
    return new Promise(resolve => setTimeout(resolve, actualDelay));
  }

  // 模拟错误
  simulateError() {
    if (Math.random() < this.errorRate) {
      throw new Error('网络连接异常，请稍后重试');
    }
  }

  // 生成唯一ID
  generateId() {
    return 'id_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
  }

  // ===================================================================
  // 组织架构相关API
  // ===================================================================

  /**
   * 获取组织架构树
   */
  async getOrganizations() {
    await this.delay(this.baseDelay);
    this.simulateError();

    console.log('[Mock API] 获取组织架构数据');
    return JSON.parse(JSON.stringify(mockOrganizationData));
  }

  /**
   * 获取合并规则
   */
  async getConsolidationRules() {
    await this.delay(this.baseDelay * 0.8);
    this.simulateError();

    console.log('[Mock API] 获取合并规则数据');
    return JSON.parse(JSON.stringify(mockConsolidationRules));
  }

  /**
   * 更新合并规则
   */
  async updateConsolidationRule(organizationId, rules) {
    await this.delay(this.baseDelay * 1.2);
    this.simulateError();

    console.log(`[Mock API] 更新组织 ${organizationId} 的合并规则:`, rules);

    // 模拟保存到本地存储
    mockConsolidationRules[organizationId] = { ...rules };

    return {
      success: true,
      message: '合并规则更新成功',
      data: rules,
    };
  }

  // ===================================================================
  // 财务数据相关API
  // ===================================================================

  /**
   * 获取财务数据
   */
  async getFinancialData(filters = {}) {
    await this.delay(this.baseDelay * 1.5);
    this.simulateError();

    const {
      organizationId,
      period = '2025-06',
      accountCodes = mockChartOfAccounts.slice(0, 20).map(a => a.code),
      scenarioCode = 'ACTUAL',
    } = filters;

    console.log(`[Mock API] 获取财务数据 - 组织: ${organizationId}, 期间: ${period}`);

    if (!organizationId) {
      // 返回所有组织的汇总数据
      const allOrgs = this.getAllOrganizationIds();
      return allOrgs
        .map(orgId => generateRandomFinancialData(orgId, period, mockChartOfAccounts.slice(0, 15)))
        .flat();
    }

    const accounts = mockChartOfAccounts.filter(acc => accountCodes.includes(acc.code));

    return generateRandomFinancialData(organizationId, period, accounts);
  }

  /**
   * 批量导入财务数据
   */
  async batchImportFinancialData(dataList) {
    await this.delay(this.baseDelay * 3); // 导入操作较慢
    this.simulateError();

    console.log(`[Mock API] 批量导入 ${dataList.length} 条财务数据`);

    const successCount = Math.floor(dataList.length * 0.95); // 95%成功率
    const errorCount = dataList.length - successCount;

    return {
      success: true,
      successCount,
      errorCount,
      processTime: this.baseDelay * 3,
      errors:
        errorCount > 0
          ? [
            { row: 5, message: '科目代码不存在' },
            { row: 12, message: '金额格式错误' },
          ]
          : [],
    };
  }

  // ===================================================================
  // 合并计算相关API
  // ===================================================================

  /**
   * 执行合并计算
   */
  async performConsolidation(request) {
    await this.delay(this.baseDelay * 4); // 合并计算较慢
    this.simulateError();

    const { parentOrgId, period, consolidationMethod } = request;
    console.log(
      `[Mock API] 执行合并计算 - 组织: ${parentOrgId}, 期间: ${period}, 方法: ${consolidationMethod}`,
    );

    const taskId = this.generateId();

    // 模拟合并结果
    const consolidatedData = mockChartOfAccounts.slice(0, 20).map(account => ({
      accountCode: account.code,
      accountName: account.name,
      consolidatedAmount: Math.round((Math.random() - 0.3) * ******** * 100) / 100,
      currency: 'CNY',
      period: period,
    }));

    const eliminationEntries = [
      {
        id: this.generateId(),
        type: 'INVESTMENT',
        description: '投资与权益抵消',
        drAccountCode: '3001',
        drAccountName: '实收资本',
        crAccountCode: '1701',
        crAccountName: '长期股权投资',
        amount: ********,
        currency: 'CNY',
      },
      {
        id: this.generateId(),
        type: 'INTERCOMPANY_REVENUE',
        description: '内部销售收入抵消',
        drAccountCode: '4001',
        drAccountName: '主营业务收入',
        crAccountCode: '5001',
        crAccountName: '主营业务成本',
        amount: 8500000,
        currency: 'CNY',
      },
      {
        id: this.generateId(),
        type: 'INTERCOMPANY_BALANCE',
        description: '内部往来抵消',
        drAccountCode: '2121',
        drAccountName: '应付账款',
        crAccountCode: '1122',
        crAccountName: '应收账款',
        amount: 3200000,
        currency: 'CNY',
      },
    ];

    return {
      taskId,
      status: 'COMPLETED',
      consolidatedData,
      eliminationEntries,
      processTime: this.baseDelay * 4,
      summary: {
        totalAssets: consolidatedData
          .filter(d => d.accountCode.startsWith('1'))
          .reduce((sum, d) => sum + Math.abs(d.consolidatedAmount), 0),
        totalLiabilities: consolidatedData
          .filter(d => d.accountCode.startsWith('2'))
          .reduce((sum, d) => sum + Math.abs(d.consolidatedAmount), 0),
        totalEquity: consolidatedData
          .filter(d => d.accountCode.startsWith('3'))
          .reduce((sum, d) => sum + Math.abs(d.consolidatedAmount), 0),
      },
    };
  }

  /**
   * 获取合并任务状态
   */
  async getConsolidationTaskStatus(taskId) {
    await this.delay(this.baseDelay * 0.5);

    console.log(`[Mock API] 获取合并任务状态: ${taskId}`);

    return {
      taskId,
      status: 'COMPLETED',
      progress: 100,
      startTime: new Date(Date.now() - 300000).toISOString(),
      endTime: new Date().toISOString(),
      message: '合并计算完成',
    };
  }

  // ===================================================================
  // 报表生成相关API
  // ===================================================================

  /**
   * 生成报表
   */
  async generateReport(template, parameters) {
    await this.delay(this.baseDelay * 5); // 报表生成较慢
    this.simulateError();

    console.log(`[Mock API] 生成报表 - 模板: ${template}`, parameters);

    const reportId = this.generateId();
    const fileName = `report_${template}_${parameters.period}_${Date.now()}.xlsx`;

    return {
      reportId,
      fileName,
      downloadUrl: `/mock/reports/${fileName}`,
      status: 'COMPLETED',
      fileSize: Math.floor(Math.random() * 5000000) + 1000000, // 1-6MB
      generatedAt: new Date().toISOString(),
      parameters,
    };
  }

  /**
   * 获取报表模板列表
   */
  async getReportTemplates() {
    await this.delay(this.baseDelay * 0.8);

    console.log('[Mock API] 获取报表模板列表');

    return [
      {
        id: 'BS_STANDARD',
        name: '标准资产负债表',
        type: 'BALANCE_SHEET',
        category: '财务报表',
        description: '按照企业会计准则编制的标准资产负债表',
        previewImage: '/images/templates/balance-sheet.png',
      },
      {
        id: 'IS_STANDARD',
        name: '标准利润表',
        type: 'INCOME_STATEMENT',
        category: '财务报表',
        description: '按照企业会计准则编制的标准利润表',
        previewImage: '/images/templates/income-statement.png',
      },
      {
        id: 'CF_STANDARD',
        name: '现金流量表',
        type: 'CASH_FLOW',
        category: '财务报表',
        description: '现金流量表（直接法）',
        previewImage: '/images/templates/cash-flow.png',
      },
      {
        id: 'BS_CONSOLIDATED',
        name: '合并资产负债表',
        type: 'CONSOLIDATED_BALANCE_SHEET',
        category: '合并报表',
        description: '集团合并资产负债表',
        previewImage: '/images/templates/consolidated-bs.png',
      },
      {
        id: 'IS_CONSOLIDATED',
        name: '合并利润表',
        type: 'CONSOLIDATED_INCOME_STATEMENT',
        category: '合并报表',
        description: '集团合并利润表',
        previewImage: '/images/templates/consolidated-is.png',
      },
    ];
  }

  // ===================================================================
  // 工具方法
  // ===================================================================

  /**
   * 获取所有组织ID（用于生成测试数据）
   */
  getAllOrganizationIds() {
    const ids = [];

    const traverse = nodes => {
      nodes.forEach(node => {
        ids.push(node.id);
        if (node.children) {
          traverse(node.children);
        }
      });
    };

    traverse(mockOrganizationData);
    return ids;
  }

  /**
   * 获取会计科目列表
   */
  async getChartOfAccounts() {
    await this.delay(this.baseDelay * 0.6);

    console.log('[Mock API] 获取会计科目列表');
    return JSON.parse(JSON.stringify(mockChartOfAccounts));
  }

  /**
   * 获取会计期间列表
   */
  async getAccountingPeriods() {
    await this.delay(this.baseDelay * 0.5);

    console.log('[Mock API] 获取会计期间列表');

    const periods = [];
    for (let year = 2023; year <= 2025; year++) {
      for (let month = 1; month <= 12; month++) {
        periods.push({
          id: `${year}-${month.toString().padStart(2, '0')}`,
          year,
          month,
          name: `${year}年${month}月`,
          status: year === 2025 && month > 8 ? 'OPEN' : 'CLOSED',
        });
      }
    }

    return periods;
  }

  // ===================================================================
  // 合并处理相关API
  // ===================================================================

  /**
   * 获取合并任务列表
   */
  async getConsolidationTasks() {
    await this.delay(this.baseDelay);

    console.log('[Mock API] 获取合并任务列表');
    return [
      {
        id: 1,
        name: '2025年6月合并任务',
        description: '母公司及子公司A、B的合并处理',
        period: '2025-06',
        companies: ['母公司', '子公司A', '子公司B'],
        mergeMethod: 'full',
        status: 'completed',
        progress: 100,
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
      }
    ];
  }

  /**
   * 创建合并任务
   */
  async createConsolidationTask(taskData) {
    await this.delay(this.baseDelay * 1.5);

    console.log('[Mock API] 创建合并任务:', taskData);
    return {
      success: true,
      id: Date.now(),
      message: '合并任务创建成功'
    };
  }

  /**
   * 启动合并任务
   */
  async startConsolidationTask(taskId) {
    await this.delay(this.baseDelay);

    console.log(`[Mock API] 启动合并任务: ${taskId}`);
    return { success: true };
  }

  /**
   * 暂停合并任务
   */
  async pauseConsolidationTask(taskId) {
    await this.delay(this.baseDelay);

    console.log(`[Mock API] 暂停合并任务: ${taskId}`);
    return { success: true };
  }

  /**
   * 停止合并任务
   */
  async stopConsolidationTask(taskId) {
    await this.delay(this.baseDelay);

    console.log(`[Mock API] 停止合并任务: ${taskId}`);
    return { success: true };
  }
}

// 导出单例实例
export const lucaNetMockApi = new LucaNetMockApiService();

// 默认导出类（用于测试）
export default LucaNetMockApiService;

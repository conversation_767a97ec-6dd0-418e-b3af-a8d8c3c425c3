/**
 * 财务数据管理主页面
 * 
 * 功能特性：
 * - 财务数据录入（手动录入、Excel批量导入）
 * - 数据查询与筛选
 * - 批量编辑功能
 * - 数据验证与审核
 * - 版本历史管理
 */

import React, { useState, useEffect, useMemo } from 'react';
import {
  Container,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Tabs,
  Tab,
  Box,
  Alert,
  Chip,
  IconButton,
  Tooltip,
  CircularProgress,
  Snackbar,
} from '@mui/material';
import {
  Add as AddIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Edit as EditIcon,
  History as HistoryIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';

import { lucaNetMockApi } from '../../../services/lucanet/mockApiService';
import DataEntryForm from './components/DataEntryForm';
import DataImportDialog from './components/DataImportDialog';
import DataTable from './components/DataTable';
import DataFilterPanel from './components/DataFilterPanel';
import BatchEditDialog from './components/BatchEditDialog';
import VersionHistoryDialog from './components/VersionHistoryDialog';

// 标签页面板组件
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`financial-data-tabpanel-${index}`}
      aria-labelledby={`financial-data-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

// 主组件
const FinancialDataManagement = () => {
  // 状态管理
  const [currentTab, setCurrentTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [financialData, setFinancialData] = useState([]);
  const [organizations, setOrganizations] = useState([]);
  const [chartOfAccounts, setChartOfAccounts] = useState([]);
  const [accountingPeriods, setAccountingPeriods] = useState([]);
  
  // 对话框状态
  const [showDataEntry, setShowDataEntry] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [showBatchEdit, setShowBatchEdit] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  
  // 筛选状态
  const [filters, setFilters] = useState({
    organizationId: '',
    period: '2025-06',
    accountType: '',
    status: '',
    dataSource: '',
  });
  
  // 选中的数据行
  const [selectedRows, setSelectedRows] = useState([]);
  
  // 通知状态
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'info',
  });

  // 初始化数据
  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    setLoading(true);
    try {
      const [orgsData, accountsData, periodsData] = await Promise.all([
        lucaNetMockApi.getOrganizations(),
        lucaNetMockApi.getChartOfAccounts(),
        lucaNetMockApi.getAccountingPeriods(),
      ]);
      
      setOrganizations(orgsData);
      setChartOfAccounts(accountsData);
      setAccountingPeriods(periodsData);
      
      // 加载初始财务数据
      await loadFinancialData();
    } catch (err) {
      setError('初始化数据失败: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  const loadFinancialData = async () => {
    try {
      setLoading(true);
      const data = await lucaNetMockApi.getFinancialData(filters);
      setFinancialData(data);
    } catch (err) {
      showNotification('加载财务数据失败: ' + err.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  // 数据统计
  const statistics = useMemo(() => {
    const totalRecords = financialData.length;
    const approvedRecords = financialData.filter(d => d.status === 'APPROVED').length;
    const draftRecords = financialData.filter(d => d.status === 'DRAFT').length;
    const totalAmount = financialData.reduce((sum, d) => sum + Math.abs(d.localAmount || 0), 0);
    
    return {
      totalRecords,
      approvedRecords,
      draftRecords,
      totalAmount: totalAmount.toLocaleString('zh-CN', {
        style: 'currency',
        currency: 'CNY',
        minimumFractionDigits: 2,
      }),
    };
  }, [financialData]);

  // 事件处理函数
  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleRefresh = () => {
    loadFinancialData();
  };

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const handleDataSaved = () => {
    showNotification('数据保存成功', 'success');
    loadFinancialData();
    setShowDataEntry(false);
  };

  const handleImportCompleted = (result) => {
    showNotification(
      `导入完成：成功 ${result.successCount} 条，失败 ${result.errorCount} 条`,
      result.errorCount > 0 ? 'warning' : 'success'
    );
    loadFinancialData();
    setShowImportDialog(false);
  };

  const handleBatchEditCompleted = () => {
    showNotification('批量编辑完成', 'success');
    loadFinancialData();
    setShowBatchEdit(false);
    setSelectedRows([]);
  };

  const showNotification = (message, severity = 'info') => {
    setNotification({
      open: true,
      message,
      severity,
    });
  };

  const closeNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // 应用筛选条件
  useEffect(() => {
    loadFinancialData();
  }, [filters]);

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      {/* 页面标题和操作按钮 */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1" gutterBottom>
          财务数据管理
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="刷新数据">
            <IconButton onClick={handleRefresh} disabled={loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Tooltip title="筛选">
            <IconButton 
              onClick={() => setShowFilterPanel(true)}
              color={showFilterPanel ? 'primary' : 'default'}
            >
              <FilterIcon />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            onClick={() => setShowImportDialog(true)}
          >
            批量导入
          </Button>
          
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setShowDataEntry(true)}
          >
            新增数据
          </Button>
        </Box>
      </Box>

      {/* 错误提示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* 数据统计卡片 */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                总记录数
              </Typography>
              <Typography variant="h5" component="div">
                {statistics.totalRecords}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                已审核
              </Typography>
              <Typography variant="h5" component="div" color="success.main">
                {statistics.approvedRecords}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                草稿
              </Typography>
              <Typography variant="h5" component="div" color="warning.main">
                {statistics.draftRecords}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="textSecondary" gutterBottom>
                总金额
              </Typography>
              <Typography variant="h6" component="div">
                {statistics.totalAmount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* 主要内容区域 */}
      <Card>
        <CardContent>
          {/* 标签页 */}
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={currentTab} onChange={handleTabChange}>
              <Tab label="数据列表" />
              <Tab label="数据分析" />
              <Tab label="审核状态" />
            </Tabs>
          </Box>

          {/* 标签页内容 */}
          <TabPanel value={currentTab} index={0}>
            {/* 操作工具栏 */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box>
                {selectedRows.length > 0 && (
                  <>
                    <Chip 
                      label={`已选择 ${selectedRows.length} 项`} 
                      color="primary" 
                      sx={{ mr: 1 }} 
                    />
                    <Button
                      size="small"
                      startIcon={<EditIcon />}
                      onClick={() => setShowBatchEdit(true)}
                      disabled={selectedRows.length === 0}
                    >
                      批量编辑
                    </Button>
                  </>
                )}
              </Box>
              
              <Box>
                <Button
                  size="small"
                  startIcon={<HistoryIcon />}
                  onClick={() => setShowVersionHistory(true)}
                >
                  版本历史
                </Button>
                <Button
                  size="small"
                  startIcon={<DownloadIcon />}
                  sx={{ ml: 1 }}
                >
                  导出Excel
                </Button>
              </Box>
            </Box>

            {/* 数据表格 */}
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                <CircularProgress />
              </Box>
            ) : (
              <DataTable
                data={financialData}
                organizations={organizations}
                chartOfAccounts={chartOfAccounts}
                selectedRows={selectedRows}
                onSelectionChange={setSelectedRows}
                onRowEdit={(row) => {
                  // 编辑单行数据的逻辑
                  console.log('编辑行:', row);
                }}
              />
            )}
          </TabPanel>

          <TabPanel value={currentTab} index={1}>
            <Typography variant="h6" gutterBottom>
              数据分析功能
            </Typography>
            <Alert severity="info">
              数据分析面板正在开发中，将包含财务数据的图表分析、趋势分析等功能。
            </Alert>
          </TabPanel>

          <TabPanel value={currentTab} index={2}>
            <Typography variant="h6" gutterBottom>
              审核状态管理
            </Typography>
            <Alert severity="info">
              审核状态管理面板正在开发中，将包含数据审核工作流、审核历史等功能。
            </Alert>
          </TabPanel>
        </CardContent>
      </Card>

      {/* 对话框组件 */}
      {showDataEntry && (
        <DataEntryForm
          open={showDataEntry}
          onClose={() => setShowDataEntry(false)}
          onSave={handleDataSaved}
          organizations={organizations}
          chartOfAccounts={chartOfAccounts}
          accountingPeriods={accountingPeriods}
        />
      )}

      {showImportDialog && (
        <DataImportDialog
          open={showImportDialog}
          onClose={() => setShowImportDialog(false)}
          onImportCompleted={handleImportCompleted}
          organizations={organizations}
          chartOfAccounts={chartOfAccounts}
          accountingPeriods={accountingPeriods}
        />
      )}

      {showBatchEdit && (
        <BatchEditDialog
          open={showBatchEdit}
          onClose={() => setShowBatchEdit(false)}
          onCompleted={handleBatchEditCompleted}
          selectedData={financialData.filter(item => selectedRows.includes(item.id))}
          chartOfAccounts={chartOfAccounts}
        />
      )}

      {showVersionHistory && (
        <VersionHistoryDialog
          open={showVersionHistory}
          onClose={() => setShowVersionHistory(false)}
        />
      )}

      {showFilterPanel && (
        <DataFilterPanel
          open={showFilterPanel}
          onClose={() => setShowFilterPanel(false)}
          filters={filters}
          onFiltersChange={handleFilterChange}
          organizations={organizations}
          chartOfAccounts={chartOfAccounts}
          accountingPeriods={accountingPeriods}
        />
      )}

      {/* 通知组件 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={closeNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Alert onClose={closeNotification} severity={notification.severity}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default FinancialDataManagement;
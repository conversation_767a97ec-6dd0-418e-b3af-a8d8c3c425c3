/**
 * 财务数据手动录入表单
 * 
 * 功能特性：
 * - 分步骤的表单向导
 * - 实时数据验证
 * - 自动计算功能
 * - 草稿保存功能
 * - 批量录入模式
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Stepper,
  Step,
  StepLabel,
  Box,
  Typography,
  Grid,
  TextField,
  Autocomplete,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Chip,
  Divider,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Switch,
  FormControlLabel,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Calculate as CalculateIcon,
  ContentCopy as CopyIcon,
} from '@mui/icons-material';

import { lucaNetMockApi } from '../../../../services/lucanet/mockApiService';

// 表单步骤
const STEPS = ['基本信息', '财务数据', '确认提交'];

// 数据验证规则
const validateFormData = (data) => {
  const errors = {};
  
  if (!data.organizationId) {
    errors.organizationId = '请选择组织';
  }
  
  if (!data.period) {
    errors.period = '请选择会计期间';
  }
  
  if (!data.scenarioCode) {
    errors.scenarioCode = '请选择业务场景';
  }
  
  return errors;
};

const validateFinancialDataRow = (row) => {
  const errors = {};
  
  if (!row.accountCode) {
    errors.accountCode = '请选择科目';
  }
  
  if (row.localAmount === '' || row.localAmount === null) {
    errors.localAmount = '请输入本币金额';
  } else if (isNaN(row.localAmount)) {
    errors.localAmount = '金额格式错误';
  }
  
  if (!row.localCurrency) {
    errors.localCurrency = '请选择币种';
  }
  
  return errors;
};

// 主组件
const DataEntryForm = ({
  open,
  onClose,
  onSave,
  organizations,
  chartOfAccounts,
  accountingPeriods,
  editData = null, // 编辑模式下的初始数据
}) => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  
  // 表单基本信息
  const [formData, setFormData] = useState({
    organizationId: '',
    period: '',
    scenarioCode: 'ACTUAL',
    dataSource: 'MANUAL',
    description: '',
  });
  
  // 财务数据行
  const [financialDataRows, setFinancialDataRows] = useState([
    {
      id: 1,
      accountCode: '',
      accountName: '',
      localAmount: '',
      functionalAmount: '',
      groupAmount: '',
      localCurrency: 'CNY',
      functionalCurrency: 'CNY',
      groupCurrency: 'CNY',
      notes: '',
    },
  ]);
  
  // 批量录入模式
  const [batchMode, setBatchMode] = useState(false);
  
  // 汇率信息
  const [exchangeRates, setExchangeRates] = useState({
    'USD-CNY': 7.2,
    'HKD-CNY': 0.92,
    'EUR-CNY': 7.8,
  });

  // 初始化编辑数据
  useEffect(() => {
    if (editData) {
      setFormData({
        organizationId: editData.organizationId || '',
        period: editData.period || '',
        scenarioCode: editData.scenarioCode || 'ACTUAL',
        dataSource: editData.dataSource || 'MANUAL',
        description: editData.description || '',
      });
      
      if (editData.financialData) {
        setFinancialDataRows(editData.financialData.map((item, index) => ({
          id: index + 1,
          ...item,
        })));
      }
    }
  }, [editData]);

  // 重置表单
  const resetForm = () => {
    setActiveStep(0);
    setErrors({});
    setFormData({
      organizationId: '',
      period: '',
      scenarioCode: 'ACTUAL',
      dataSource: 'MANUAL',
      description: '',
    });
    setFinancialDataRows([
      {
        id: 1,
        accountCode: '',
        accountName: '',
        localAmount: '',
        functionalAmount: '',
        groupAmount: '',
        localCurrency: 'CNY',
        functionalCurrency: 'CNY',
        groupCurrency: 'CNY',
        notes: '',
      },
    ]);
  };

  // 处理表单字段变化
  const handleFormDataChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    // 清除相关错误
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // 处理财务数据行变化
  const handleRowChange = (rowId, field, value) => {
    setFinancialDataRows(prev => prev.map(row => {
      if (row.id === rowId) {
        const updatedRow = { ...row, [field]: value };
        
        // 自动计算功能币和集团币金额
        if (field === 'localAmount' || field === 'localCurrency') {
          const localAmount = parseFloat(updatedRow.localAmount) || 0;
          const localCurrency = updatedRow.localCurrency;
          
          // 计算功能币金额（假设功能币为CNY）
          let functionalAmount = localAmount;
          if (localCurrency !== 'CNY') {
            const rateKey = `${localCurrency}-CNY`;
            const rate = exchangeRates[rateKey] || 1;
            functionalAmount = localAmount * rate;
          }
          
          updatedRow.functionalAmount = functionalAmount.toFixed(2);
          updatedRow.groupAmount = functionalAmount.toFixed(2); // 假设集团币也是CNY
        }
        
        // 自动填充科目名称
        if (field === 'accountCode') {
          const account = chartOfAccounts.find(acc => acc.code === value);
          updatedRow.accountName = account ? account.name : '';
        }
        
        return updatedRow;
      }
      return row;
    }));
  };

  // 添加新行
  const handleAddRow = () => {
    const newId = Math.max(...financialDataRows.map(row => row.id)) + 1;
    const newRow = {
      id: newId,
      accountCode: '',
      accountName: '',
      localAmount: '',
      functionalAmount: '',
      groupAmount: '',
      localCurrency: formData.period?.startsWith('2025') ? 'CNY' : 'CNY',
      functionalCurrency: 'CNY',
      groupCurrency: 'CNY',
      notes: '',
    };
    
    setFinancialDataRows(prev => [...prev, newRow]);
  };

  // 删除行
  const handleDeleteRow = (rowId) => {
    if (financialDataRows.length > 1) {
      setFinancialDataRows(prev => prev.filter(row => row.id !== rowId));
    }
  };

  // 复制行
  const handleCopyRow = (sourceRowId) => {
    const sourceRow = financialDataRows.find(row => row.id === sourceRowId);
    if (sourceRow) {
      const newId = Math.max(...financialDataRows.map(row => row.id)) + 1;
      const newRow = {
        ...sourceRow,
        id: newId,
        localAmount: '',
        functionalAmount: '',
        groupAmount: '',
      };
      
      setFinancialDataRows(prev => [...prev, newRow]);
    }
  };

  // 下一步
  const handleNext = () => {
    if (activeStep === 0) {
      // 验证基本信息
      const validationErrors = validateFormData(formData);
      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        return;
      }
    } else if (activeStep === 1) {
      // 验证财务数据
      const rowErrors = {};
      financialDataRows.forEach(row => {
        const rowValidationErrors = validateFinancialDataRow(row);
        if (Object.keys(rowValidationErrors).length > 0) {
          rowErrors[row.id] = rowValidationErrors;
        }
      });
      
      if (Object.keys(rowErrors).length > 0) {
        setErrors({ rows: rowErrors });
        return;
      }
    }
    
    setActiveStep(prev => prev + 1);
  };

  // 上一步
  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  // 保存草稿
  const handleSaveDraft = async () => {
    setLoading(true);
    try {
      // 这里调用API保存草稿
      console.log('保存草稿:', { formData, financialDataRows });
      alert('草稿保存成功');
    } catch (error) {
      alert('保存草稿失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    setLoading(true);
    try {
      // 准备提交数据
      const submitData = financialDataRows.map(row => ({
        organizationId: formData.organizationId,
        accountCode: row.accountCode,
        accountName: row.accountName,
        period: formData.period,
        localAmount: parseFloat(row.localAmount),
        functionalAmount: parseFloat(row.functionalAmount),
        groupAmount: parseFloat(row.groupAmount),
        localCurrency: row.localCurrency,
        functionalCurrency: row.functionalCurrency,
        groupCurrency: row.groupCurrency,
        dataSource: formData.dataSource,
        scenarioCode: formData.scenarioCode,
        notes: row.notes,
        status: 'DRAFT',
      }));
      
      // 调用API保存数据
      await lucaNetMockApi.batchImportFinancialData(submitData);
      
      onSave();
      resetForm();
    } catch (error) {
      alert('提交失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // 获取组织名称
  const getOrganizationName = (orgId) => {
    const findOrgInTree = (nodes) => {
      for (const node of nodes) {
        if (node.id === orgId) return node.name;
        if (node.children) {
          const found = findOrgInTree(node.children);
          if (found) return found;
        }
      }
      return null;
    };
    return findOrgInTree(organizations);
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{ sx: { height: '90vh' } }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {editData ? '编辑财务数据' : '新增财务数据'}
          </Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<SaveIcon />}
            onClick={handleSaveDraft}
            disabled={loading}
          >
            保存草稿
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent dividers>
        {/* 步骤指示器 */}
        <Stepper activeStep={activeStep} sx={{ mb: 3 }}>
          {STEPS.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* 步骤内容 */}
        {activeStep === 0 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              基本信息设置
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Autocomplete
                  value={organizations.find(org => org.id === formData.organizationId) || null}
                  onChange={(event, newValue) => {
                    handleFormDataChange('organizationId', newValue ? newValue.id : '');
                  }}
                  options={organizations.flatMap(org => [
                    org,
                    ...(org.children || []).flatMap(child => [
                      child,
                      ...(child.children || [])
                    ])
                  ])}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      label="组织机构"
                      required
                      error={!!errors.organizationId}
                      helperText={errors.organizationId}
                    />
                  )}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required error={!!errors.period}>
                  <InputLabel>会计期间</InputLabel>
                  <Select
                    value={formData.period}
                    onChange={(e) => handleFormDataChange('period', e.target.value)}
                    label="会计期间"
                  >
                    {accountingPeriods.slice(-12).map((period) => (
                      <MenuItem key={period.id} value={period.id}>
                        {period.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>业务场景</InputLabel>
                  <Select
                    value={formData.scenarioCode}
                    onChange={(e) => handleFormDataChange('scenarioCode', e.target.value)}
                    label="业务场景"
                  >
                    <MenuItem value="ACTUAL">实际数</MenuItem>
                    <MenuItem value="BUDGET">预算数</MenuItem>
                    <MenuItem value="FORECAST">预测数</MenuItem>
                    <MenuItem value="PLAN">计划数</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>数据来源</InputLabel>
                  <Select
                    value={formData.dataSource}
                    onChange={(e) => handleFormDataChange('dataSource', e.target.value)}
                    label="数据来源"
                  >
                    <MenuItem value="MANUAL">手动录入</MenuItem>
                    <MenuItem value="IMPORT">批量导入</MenuItem>
                    <MenuItem value="INTERFACE">接口导入</MenuItem>
                    <MenuItem value="SYSTEM">系统生成</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="描述说明"
                  value={formData.description}
                  onChange={(e) => handleFormDataChange('description', e.target.value)}
                  multiline
                  rows={3}
                  placeholder="可选择性地添加数据描述、备注等信息"
                />
              </Grid>
            </Grid>
          </Box>
        )}

        {activeStep === 1 && (
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                财务数据录入
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={batchMode}
                      onChange={(e) => setBatchMode(e.target.checked)}
                    />
                  }
                  label="批量录入模式"
                />
                
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={handleAddRow}
                >
                  添加行
                </Button>
              </Box>
            </Box>
            
            <TableContainer component={Paper} sx={{ maxHeight: 500 }}>
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>科目代码</TableCell>
                    <TableCell>科目名称</TableCell>
                    <TableCell>本币金额</TableCell>
                    <TableCell>本币币种</TableCell>
                    <TableCell>功能币金额</TableCell>
                    <TableCell>集团币金额</TableCell>
                    <TableCell>备注</TableCell>
                    <TableCell>操作</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {financialDataRows.map((row) => (
                    <TableRow key={row.id}>
                      <TableCell>
                        <Autocomplete
                          size="small"
                          sx={{ minWidth: 120 }}
                          value={chartOfAccounts.find(acc => acc.code === row.accountCode) || null}
                          onChange={(event, newValue) => {
                            handleRowChange(row.id, 'accountCode', newValue ? newValue.code : '');
                          }}
                          options={chartOfAccounts}
                          getOptionLabel={(option) => option.code}
                          renderInput={(params) => (
                            <TextField
                              {...params}
                              error={!!errors.rows?.[row.id]?.accountCode}
                              placeholder="选择科目"
                            />
                          )}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Typography variant="body2" sx={{ minWidth: 120 }}>
                          {row.accountName}
                        </Typography>
                      </TableCell>
                      
                      <TableCell>
                        <TextField
                          size="small"
                          type="number"
                          value={row.localAmount}
                          onChange={(e) => handleRowChange(row.id, 'localAmount', e.target.value)}
                          error={!!errors.rows?.[row.id]?.localAmount}
                          sx={{ width: 120 }}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Select
                          size="small"
                          value={row.localCurrency}
                          onChange={(e) => handleRowChange(row.id, 'localCurrency', e.target.value)}
                          sx={{ width: 80 }}
                        >
                          <MenuItem value="CNY">CNY</MenuItem>
                          <MenuItem value="USD">USD</MenuItem>
                          <MenuItem value="HKD">HKD</MenuItem>
                          <MenuItem value="EUR">EUR</MenuItem>
                        </Select>
                      </TableCell>
                      
                      <TableCell>
                        <TextField
                          size="small"
                          value={row.functionalAmount}
                          InputProps={{ readOnly: true }}
                          sx={{ width: 120 }}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <TextField
                          size="small"
                          value={row.groupAmount}
                          InputProps={{ readOnly: true }}
                          sx={{ width: 120 }}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <TextField
                          size="small"
                          value={row.notes}
                          onChange={(e) => handleRowChange(row.id, 'notes', e.target.value)}
                          placeholder="备注"
                          sx={{ width: 100 }}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 0.5 }}>
                          <Tooltip title="复制行">
                            <IconButton
                              size="small"
                              onClick={() => handleCopyRow(row.id)}
                            >
                              <CopyIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          
                          <Tooltip title="删除行">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteRow(row.id)}
                              disabled={financialDataRows.length === 1}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
            
            {errors.rows && (
              <Alert severity="error" sx={{ mt: 2 }}>
                请修正表格中的错误后继续
              </Alert>
            )}
          </Box>
        )}

        {activeStep === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              确认提交
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                  基本信息
                </Typography>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="body2"><strong>组织:</strong> {getOrganizationName(formData.organizationId)}</Typography>
                  <Typography variant="body2"><strong>期间:</strong> {formData.period}</Typography>
                  <Typography variant="body2"><strong>场景:</strong> {formData.scenarioCode}</Typography>
                  <Typography variant="body2"><strong>来源:</strong> {formData.dataSource}</Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle1" gutterBottom>
                  数据统计
                </Typography>
                <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="body2"><strong>记录数:</strong> {financialDataRows.length}</Typography>
                  <Typography variant="body2">
                    <strong>总金额:</strong> {financialDataRows.reduce((sum, row) => sum + (parseFloat(row.localAmount) || 0), 0).toLocaleString()} CNY
                  </Typography>
                </Box>
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>
                  数据预览
                </Typography>
                <TableContainer component={Paper} sx={{ maxHeight: 300 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>科目代码</TableCell>
                        <TableCell>科目名称</TableCell>
                        <TableCell align="right">本币金额</TableCell>
                        <TableCell>币种</TableCell>
                        <TableCell>备注</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {financialDataRows.map((row) => (
                        <TableRow key={row.id}>
                          <TableCell>{row.accountCode}</TableCell>
                          <TableCell>{row.accountName}</TableCell>
                          <TableCell align="right">
                            {parseFloat(row.localAmount || 0).toLocaleString()}
                          </TableCell>
                          <TableCell>{row.localCurrency}</TableCell>
                          <TableCell>{row.notes}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Grid>
            </Grid>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          取消
        </Button>
        
        {activeStep > 0 && (
          <Button onClick={handleBack} disabled={loading}>
            上一步
          </Button>
        )}
        
        {activeStep < STEPS.length - 1 ? (
          <Button
            variant="contained"
            onClick={handleNext}
            disabled={loading}
          >
            下一步
          </Button>
        ) : (
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading}
          >
            提交保存
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default DataEntryForm;
/**
 * 版本历史对话框
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Paper,
  Box,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Divider,
} from '@mui/material';
import {
  History as HistoryIcon,
  Person as PersonIcon,
  Edit as EditIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';

// 模拟版本历史数据
const mockVersionHistory = [
  {
    id: 1,
    action: 'CREATE',
    description: '创建财务数据记录',
    user: '张三',
    timestamp: '2025-01-18 14:30:00',
    changes: {
      accountCode: '1001',
      accountName: '库存现金',
      localAmount: 125000.50,
    }
  },
  {
    id: 2,
    action: 'UPDATE',
    description: '修改金额',
    user: '李四',
    timestamp: '2025-01-18 15:45:00',
    changes: {
      localAmount: { from: 125000.50, to: 135000.50 },
      notes: { from: '', to: '调整现金余额' },
    }
  },
  {
    id: 3,
    action: 'APPROVE',
    description: '审核通过',
    user: '王五',
    timestamp: '2025-01-18 16:20:00',
    changes: {
      status: { from: 'DRAFT', to: 'APPROVED' },
    }
  },
];

const VersionHistoryDialog = ({ open, onClose }) => {
  const [historyData, setHistoryData] = useState([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      loadHistoryData();
    }
  }, [open]);

  const loadHistoryData = async () => {
    setLoading(true);
    // 模拟API调用
    setTimeout(() => {
      setHistoryData(mockVersionHistory);
      setLoading(false);
    }, 500);
  };

  const getActionIcon = (action) => {
    switch (action) {
      case 'CREATE':
        return <AddIcon />;
      case 'UPDATE':
        return <EditIcon />;
      case 'DELETE':
        return <DeleteIcon />;
      case 'APPROVE':
        return <ViewIcon />;
      default:
        return <HistoryIcon />;
    }
  };

  const getActionColor = (action) => {
    switch (action) {
      case 'CREATE':
        return 'success';
      case 'UPDATE':
        return 'primary';
      case 'DELETE':
        return 'error';
      case 'APPROVE':
        return 'success';
      default:
        return 'grey';
    }
  };

  const formatChangeValue = (value) => {
    if (typeof value === 'object' && value.from !== undefined) {
      return `${value.from} → ${value.to}`;
    }
    return value;
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <HistoryIcon />
          <Typography variant="h6">版本历史</Typography>
        </Box>
      </DialogTitle>
      
      <DialogContent dividers>
        {loading ? (
          <Typography>加载中...</Typography>
        ) : (
          <List>
            {historyData.map((item, index) => (
              <React.Fragment key={item.id}>
                <ListItem alignItems="flex-start">
                  <ListItemIcon>
                    <Avatar
                      sx={{
                        bgcolor: `${getActionColor(item.action)}.main`,
                        width: 40,
                        height: 40,
                      }}
                    >
                      {getActionIcon(item.action)}
                    </Avatar>
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Typography variant="subtitle1">
                          {item.description}
                        </Typography>
                        <Chip 
                          label={item.action} 
                          size="small" 
                          color={getActionColor(item.action)}
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <PersonIcon fontSize="small" />
                          <Typography variant="body2" color="text.secondary">
                            {item.user} · {item.timestamp}
                          </Typography>
                        </Box>
                        
                        {item.changes && (
                          <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                            <Typography variant="subtitle2" gutterBottom>
                              变更内容:
                            </Typography>
                            {Object.entries(item.changes).map(([field, value]) => (
                              <Typography key={field} variant="body2" sx={{ ml: 1 }}>
                                <strong>{field}:</strong> {formatChangeValue(value)}
                              </Typography>
                            ))}
                          </Paper>
                        )}
                      </Box>
                    }
                  />
                </ListItem>
                
                {index < historyData.length - 1 && <Divider sx={{ my: 2 }} />}
              </React.Fragment>
            ))}
          </List>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>关闭</Button>
      </DialogActions>
    </Dialog>
  );
};

export default VersionHistoryDialog;
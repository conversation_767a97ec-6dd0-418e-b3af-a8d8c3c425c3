/**
 * 数据筛选面板
 */

import React from 'react';
import {
  Drawer,
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  Divider,
  Autocomplete,
  Chip,
} from '@mui/material';

const DataFilterPanel = ({
  open,
  onClose,
  filters,
  onFiltersChange,
  organizations,
  chartOfAccounts,
  accountingPeriods,
}) => {
  const handleFilterChange = (field, value) => {
    onFiltersChange({ [field]: value });
  };

  const handleClearFilters = () => {
    onFiltersChange({
      organizationId: '',
      period: '',
      accountType: '',
      status: '',
      dataSource: '',
    });
  };

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{ sx: { width: 350, p: 3 } }}
    >
      <Typography variant="h6" gutterBottom>
        数据筛选
      </Typography>
      
      <Divider sx={{ mb: 3 }} />
      
      {/* 组织筛选 */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>组织机构</InputLabel>
        <Select
          value={filters.organizationId}
          onChange={(e) => handleFilterChange('organizationId', e.target.value)}
          label="组织机构"
        >
          <MenuItem value="">全部</MenuItem>
          {organizations.flatMap(org => [
            <MenuItem key={org.id} value={org.id}>{org.name}</MenuItem>,
            ...(org.children || []).map(child => (
              <MenuItem key={child.id} value={child.id}>
                　{child.name}
              </MenuItem>
            ))
          ])}
        </Select>
      </FormControl>
      
      {/* 期间筛选 */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>会计期间</InputLabel>
        <Select
          value={filters.period}
          onChange={(e) => handleFilterChange('period', e.target.value)}
          label="会计期间"
        >
          <MenuItem value="">全部</MenuItem>
          {accountingPeriods.slice(-12).map((period) => (
            <MenuItem key={period.id} value={period.id}>
              {period.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      
      {/* 科目类型筛选 */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>科目类型</InputLabel>
        <Select
          value={filters.accountType}
          onChange={(e) => handleFilterChange('accountType', e.target.value)}
          label="科目类型"
        >
          <MenuItem value="">全部</MenuItem>
          <MenuItem value="ASSET">资产类</MenuItem>
          <MenuItem value="LIABILITY">负债类</MenuItem>
          <MenuItem value="EQUITY">所有者权益类</MenuItem>
          <MenuItem value="REVENUE">收入类</MenuItem>
          <MenuItem value="EXPENSE">费用类</MenuItem>
        </Select>
      </FormControl>
      
      {/* 状态筛选 */}
      <FormControl fullWidth sx={{ mb: 2 }}>
        <InputLabel>审核状态</InputLabel>
        <Select
          value={filters.status}
          onChange={(e) => handleFilterChange('status', e.target.value)}
          label="审核状态"
        >
          <MenuItem value="">全部</MenuItem>
          <MenuItem value="DRAFT">草稿</MenuItem>
          <MenuItem value="PENDING">待审核</MenuItem>
          <MenuItem value="APPROVED">已审核</MenuItem>
          <MenuItem value="REJECTED">已拒绝</MenuItem>
        </Select>
      </FormControl>
      
      {/* 数据来源筛选 */}
      <FormControl fullWidth sx={{ mb: 3 }}>
        <InputLabel>数据来源</InputLabel>
        <Select
          value={filters.dataSource}
          onChange={(e) => handleFilterChange('dataSource', e.target.value)}
          label="数据来源"
        >
          <MenuItem value="">全部</MenuItem>
          <MenuItem value="MANUAL">手动录入</MenuItem>
          <MenuItem value="IMPORT">批量导入</MenuItem>
          <MenuItem value="INTERFACE">接口导入</MenuItem>
          <MenuItem value="SYSTEM">系统生成</MenuItem>
        </Select>
      </FormControl>
      
      <Box sx={{ display: 'flex', gap: 1 }}>
        <Button
          variant="outlined"
          onClick={handleClearFilters}
          fullWidth
        >
          清除筛选
        </Button>
        <Button
          variant="contained"
          onClick={onClose}
          fullWidth
        >
          应用筛选
        </Button>
      </Box>
    </Drawer>
  );
};

export default DataFilterPanel;
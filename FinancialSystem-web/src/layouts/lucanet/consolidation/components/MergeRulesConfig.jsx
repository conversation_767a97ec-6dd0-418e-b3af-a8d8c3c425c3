import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  Tooltip,
  Slider,
  RadioGroup,
  Radio,
  FormLabel,
  Checkbox,
  FormGroup
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Settings as SettingsIcon,
  Rule as RuleIcon,
  AccountTree as AccountTreeIcon,
  Percent as PercentIcon,
  Calculate as CalculateIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  Visibility as VisibilityIcon,
  ContentCopy as CopyIcon,
  Upload as UploadIcon,
  Download as DownloadIcon
} from '@mui/icons-material';

// 合并方法配置
const consolidationMethods = {
  full: {
    name: '全额合并法',
    description: '将子公司100%的资产、负债、收入、费用纳入合并范围',
    icon: <AccountTreeIcon />,
    applicableFor: ['控股超过50%的子公司']
  },
  proportional: {
    name: '比例合并法',
    description: '按持股比例将合营企业的资产、负债、收入、费用纳入合并范围',
    icon: <PercentIcon />,
    applicableFor: ['共同控制的合营企业']
  },
  equity: {
    name: '权益法',
    description: '将投资成本与投资收益纳入合并范围，不合并资产负债表',
    icon: <CalculateIcon />,
    applicableFor: ['联营企业', '重大影响的投资']
  }
};

// 规则配置对话框
const RuleConfigDialog = ({ open, onClose, rule, onSave }) => {
  const [formData, setFormData] = useState({
    name: '',
    type: 'ownership',
    method: 'full',
    conditions: [],
    parameters: {},
    isActive: true,
    priority: 1,
    description: ''
  });

  useEffect(() => {
    if (rule) {
      setFormData(rule);
    } else {
      setFormData({
        name: '',
        type: 'ownership',
        method: 'full',
        conditions: [],
        parameters: {},
        isActive: true,
        priority: 1,
        description: ''
      });
    }
  }, [rule, open]);

  const handleSave = () => {
    onSave({
      ...formData,
      id: rule?.id || Date.now(),
      updatedAt: new Date().toISOString()
    });
    onClose();
  };

  const addCondition = () => {
    setFormData({
      ...formData,
      conditions: [...formData.conditions, {
        field: 'ownership_percentage',
        operator: 'greater_than',
        value: 50,
        description: ''
      }]
    });
  };

  const updateCondition = (index, field, value) => {
    const newConditions = [...formData.conditions];
    newConditions[index] = { ...newConditions[index], [field]: value };
    setFormData({ ...formData, conditions: newConditions });
  };

  const removeCondition = (index) => {
    const newConditions = formData.conditions.filter((_, i) => i !== index);
    setFormData({ ...formData, conditions: newConditions });
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        {rule ? '编辑合并规则' : '创建合并规则'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* 基本信息 */}
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="规则名称"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>规则类型</InputLabel>
              <Select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              >
                <MenuItem value="ownership">控制权规则</MenuItem>
                <MenuItem value="influence">重大影响规则</MenuItem>
                <MenuItem value="joint_control">共同控制规则</MenuItem>
                <MenuItem value="special">特殊规则</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel>合并方法</InputLabel>
              <Select
                value={formData.method}
                onChange={(e) => setFormData({ ...formData, method: e.target.value })}
              >
                {Object.entries(consolidationMethods).map(([key, method]) => (
                  <MenuItem key={key} value={key}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {method.icon}
                      <Typography sx={{ ml: 1 }}>{method.name}</Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="优先级"
              type="number"
              value={formData.priority}
              onChange={(e) => setFormData({ ...formData, priority: parseInt(e.target.value) })}
              inputProps={{ min: 1, max: 10 }}
              helperText="1-10，数字越小优先级越高"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="规则描述"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              multiline
              rows={2}
            />
          </Grid>

          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.isActive}
                  onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
                />
              }
              label="启用规则"
            />
          </Grid>
        </Grid>

        {/* 规则条件 */}
        <Accordion sx={{ mt: 2 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">规则条件</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Box sx={{ mb: 2 }}>
              <Button
                variant="outlined"
                startIcon={<AddIcon />}
                onClick={addCondition}
                size="small"
              >
                添加条件
              </Button>
            </Box>
            {formData.conditions.map((condition, index) => (
              <Card key={index} sx={{ mb: 2, p: 2 }}>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={3}>
                    <FormControl fullWidth size="small">
                      <InputLabel>字段</InputLabel>
                      <Select
                        value={condition.field}
                        onChange={(e) => updateCondition(index, 'field', e.target.value)}
                      >
                        <MenuItem value="ownership_percentage">持股比例</MenuItem>
                        <MenuItem value="voting_rights">表决权比例</MenuItem>
                        <MenuItem value="control_type">控制类型</MenuItem>
                        <MenuItem value="business_type">业务类型</MenuItem>
                        <MenuItem value="company_type">公司类型</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={2}>
                    <FormControl fullWidth size="small">
                      <InputLabel>操作符</InputLabel>
                      <Select
                        value={condition.operator}
                        onChange={(e) => updateCondition(index, 'operator', e.target.value)}
                      >
                        <MenuItem value="greater_than">大于</MenuItem>
                        <MenuItem value="greater_equal">大于等于</MenuItem>
                        <MenuItem value="less_than">小于</MenuItem>
                        <MenuItem value="less_equal">小于等于</MenuItem>
                        <MenuItem value="equals">等于</MenuItem>
                        <MenuItem value="not_equals">不等于</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="值"
                      value={condition.value}
                      onChange={(e) => updateCondition(index, 'value', e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={3}>
                    <TextField
                      fullWidth
                      label="说明"
                      value={condition.description || ''}
                      onChange={(e) => updateCondition(index, 'description', e.target.value)}
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={1}>
                    <IconButton
                      color="error"
                      onClick={() => removeCondition(index)}
                      size="small"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Grid>
                </Grid>
              </Card>
            ))}
          </AccordionDetails>
        </Accordion>

        {/* 合并方法参数 */}
        <Accordion sx={{ mt: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="h6">合并参数</Typography>
          </AccordionSummary>
          <AccordionDetails>
            {formData.method === 'full' && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.parameters.includeMinorityInterest !== false}
                        onChange={(e) => setFormData({
                          ...formData,
                          parameters: {
                            ...formData.parameters,
                            includeMinorityInterest: e.target.checked
                          }
                        })}
                      />
                    }
                    label="计算少数股东权益"
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formData.parameters.goodwillCalculation !== false}
                        onChange={(e) => setFormData({
                          ...formData,
                          parameters: {
                            ...formData.parameters,
                            goodwillCalculation: e.target.checked
                          }
                        })}
                      />
                    }
                    label="计算商誉"
                  />
                </Grid>
              </Grid>
            )}
            
            {formData.method === 'proportional' && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <Typography gutterBottom>持股比例</Typography>
                  <Slider
                    value={formData.parameters.ownershipPercentage || 50}
                    onChange={(e, value) => setFormData({
                      ...formData,
                      parameters: {
                        ...formData.parameters,
                        ownershipPercentage: value
                      }
                    })}
                    min={0}
                    max={100}
                    step={1}
                    marks
                    valueLabelDisplay="auto"
                    valueLabelFormat={(value) => `${value}%`}
                  />
                </Grid>
              </Grid>
            )}
            
            {formData.method === 'equity' && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl component="fieldset">
                    <FormLabel component="legend">权益法计算方式</FormLabel>
                    <RadioGroup
                      value={formData.parameters.equityMethod || 'cost_plus_income'}
                      onChange={(e) => setFormData({
                        ...formData,
                        parameters: {
                          ...formData.parameters,
                          equityMethod: e.target.value
                        }
                      })}
                    >
                      <FormControlLabel 
                        value="cost_plus_income" 
                        control={<Radio />} 
                        label="成本加权益变动法" 
                      />
                      <FormControlLabel 
                        value="fair_value" 
                        control={<Radio />} 
                        label="公允价值法" 
                      />
                    </RadioGroup>
                  </FormControl>
                </Grid>
              </Grid>
            )}
          </AccordionDetails>
        </Accordion>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} startIcon={<CancelIcon />}>取消</Button>
        <Button onClick={handleSave} variant="contained" startIcon={<SaveIcon />}>
          保存
        </Button>
      </DialogActions>
    </Dialog>
  );
};

// 规则测试对话框
const RuleTestDialog = ({ open, onClose, rule }) => {
  const [testData, setTestData] = useState({
    ownershipPercentage: 60,
    votingRights: 60,
    controlType: 'actual_control',
    businessType: 'manufacturing',
    companyType: 'subsidiary'
  });
  const [testResult, setTestResult] = useState(null);

  const runTest = () => {
    // 模拟规则测试
    const result = {
      isMatched: true,
      appliedMethod: rule?.method || 'full',
      calculations: {
        minorityInterest: 40,
        goodwill: 5000000,
        consolidationPercentage: 100
      },
      messages: [
        { type: 'success', text: '规则匹配成功' },
        { type: 'info', text: '将使用全额合并法' },
        { type: 'warning', text: '请注意少数股东权益的计算' }
      ]
    };
    setTestResult(result);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>规则测试</DialogTitle>
      <DialogContent>
        <Typography variant="h6" gutterBottom>
          测试数据输入
        </Typography>
        <Grid container spacing={2} sx={{ mb: 3 }}>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="持股比例 (%)"
              type="number"
              value={testData.ownershipPercentage}
              onChange={(e) => setTestData({ 
                ...testData, 
                ownershipPercentage: parseFloat(e.target.value) 
              })}
              inputProps={{ min: 0, max: 100 }}
            />
          </Grid>
          <Grid item xs={6}>
            <TextField
              fullWidth
              label="表决权比例 (%)"
              type="number"
              value={testData.votingRights}
              onChange={(e) => setTestData({ 
                ...testData, 
                votingRights: parseFloat(e.target.value) 
              })}
              inputProps={{ min: 0, max: 100 }}
            />
          </Grid>
          <Grid item xs={6}>
            <FormControl fullWidth>
              <InputLabel>控制类型</InputLabel>
              <Select
                value={testData.controlType}
                onChange={(e) => setTestData({ ...testData, controlType: e.target.value })}
              >
                <MenuItem value="actual_control">实际控制</MenuItem>
                <MenuItem value="joint_control">共同控制</MenuItem>
                <MenuItem value="significant_influence">重大影响</MenuItem>
                <MenuItem value="no_control">无控制</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={6}>
            <FormControl fullWidth>
              <InputLabel>业务类型</InputLabel>
              <Select
                value={testData.businessType}
                onChange={(e) => setTestData({ ...testData, businessType: e.target.value })}
              >
                <MenuItem value="manufacturing">制造业</MenuItem>
                <MenuItem value="service">服务业</MenuItem>
                <MenuItem value="finance">金融业</MenuItem>
                <MenuItem value="real_estate">房地产</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>

        <Button variant="contained" onClick={runTest} sx={{ mb: 3 }}>
          运行测试
        </Button>

        {testResult && (
          <Box>
            <Typography variant="h6" gutterBottom>
              测试结果
            </Typography>
            <Alert 
              severity={testResult.isMatched ? 'success' : 'error'} 
              sx={{ mb: 2 }}
            >
              {testResult.isMatched ? '规则匹配成功' : '规则不匹配'}
            </Alert>
            
            {testResult.isMatched && (
              <Card sx={{ mb: 2 }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    应用方法: {consolidationMethods[testResult.appliedMethod]?.name}
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={4}>
                      <Typography variant="body2">
                        合并比例: {testResult.calculations.consolidationPercentage}%
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2">
                        少数股东权益: {testResult.calculations.minorityInterest}%
                      </Typography>
                    </Grid>
                    <Grid item xs={4}>
                      <Typography variant="body2">
                        商誉: {testResult.calculations.goodwill.toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            )}

            <List dense>
              {testResult.messages.map((message, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    {message.type === 'success' && <CheckCircleIcon color="success" />}
                    {message.type === 'warning' && <WarningIcon color="warning" />}
                    {message.type === 'error' && <ErrorIcon color="error" />}
                    {message.type === 'info' && <InfoIcon color="info" />}
                  </ListItemIcon>
                  <ListItemText primary={message.text} />
                </ListItem>
              ))}
            </List>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>关闭</Button>
      </DialogActions>
    </Dialog>
  );
};

// 主组件
const MergeRulesConfig = () => {
  const [tabValue, setTabValue] = useState(0);
  const [rules, setRules] = useState([]);
  const [ruleDialogOpen, setRuleDialogOpen] = useState(false);
  const [testDialogOpen, setTestDialogOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState(null);

  // 模拟数据
  useEffect(() => {
    setRules([
      {
        id: 1,
        name: '控股子公司规则',
        type: 'ownership',
        method: 'full',
        priority: 1,
        isActive: true,
        description: '持股比例超过50%的控股子公司采用全额合并法',
        conditions: [
          { field: 'ownership_percentage', operator: 'greater_than', value: 50 },
          { field: 'control_type', operator: 'equals', value: 'actual_control' }
        ],
        parameters: { includeMinorityInterest: true, goodwillCalculation: true },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 2,
        name: '合营企业规则',
        type: 'joint_control',
        method: 'proportional',
        priority: 2,
        isActive: true,
        description: '共同控制的合营企业采用比例合并法',
        conditions: [
          { field: 'control_type', operator: 'equals', value: 'joint_control' },
          { field: 'ownership_percentage', operator: 'greater_equal', value: 20 }
        ],
        parameters: { ownershipPercentage: 50 },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 3,
        name: '联营企业规则',
        type: 'influence',
        method: 'equity',
        priority: 3,
        isActive: true,
        description: '重大影响的联营企业采用权益法',
        conditions: [
          { field: 'ownership_percentage', operator: 'greater_equal', value: 20 },
          { field: 'ownership_percentage', operator: 'less_equal', value: 50 }
        ],
        parameters: { equityMethod: 'cost_plus_income' },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ]);
  }, []);

  const handleSaveRule = (ruleData) => {
    if (selectedRule) {
      setRules(rules => rules.map(rule =>
        rule.id === selectedRule.id ? ruleData : rule
      ));
    } else {
      setRules(rules => [...rules, ruleData]);
    }
  };

  const handleDeleteRule = (ruleId) => {
    setRules(rules => rules.filter(rule => rule.id !== ruleId));
  };

  const handleToggleRule = (ruleId) => {
    setRules(rules => rules.map(rule =>
      rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule
    ));
  };

  const renderRulesTab = () => (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          <RuleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          合并规则配置
        </Typography>
        <Box>
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            sx={{ mr: 1 }}
          >
            导入
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            sx={{ mr: 1 }}
          >
            导出
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setSelectedRule(null);
              setRuleDialogOpen(true);
            }}
          >
            创建规则
          </Button>
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>规则名称</TableCell>
              <TableCell>类型</TableCell>
              <TableCell>合并方法</TableCell>
              <TableCell>优先级</TableCell>
              <TableCell>状态</TableCell>
              <TableCell>更新时间</TableCell>
              <TableCell>操作</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rules
              .sort((a, b) => a.priority - b.priority)
              .map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell>
                    <Typography variant="subtitle2">{rule.name}</Typography>
                    <Typography variant="caption" color="textSecondary">
                      {rule.description}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={
                        rule.type === 'ownership' ? '控制权' :
                        rule.type === 'influence' ? '重大影响' :
                        rule.type === 'joint_control' ? '共同控制' : '特殊'
                      }
                      size="small"
                      color="primary"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {consolidationMethods[rule.method]?.icon}
                      <Typography sx={{ ml: 1 }}>
                        {consolidationMethods[rule.method]?.name}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>{rule.priority}</TableCell>
                  <TableCell>
                    <Switch
                      checked={rule.isActive}
                      onChange={() => handleToggleRule(rule.id)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(rule.updatedAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="测试规则">
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedRule(rule);
                          setTestDialogOpen(true);
                        }}
                      >
                        <VisibilityIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="编辑规则">
                      <IconButton
                        size="small"
                        onClick={() => {
                          setSelectedRule(rule);
                          setRuleDialogOpen(true);
                        }}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="复制规则">
                      <IconButton
                        size="small"
                        onClick={() => {
                          const newRule = { 
                            ...rule, 
                            id: Date.now(), 
                            name: `${rule.name} (副本)`,
                            createdAt: new Date().toISOString(),
                            updatedAt: new Date().toISOString()
                          };
                          setRules(rules => [...rules, newRule]);
                        }}
                      >
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="删除规则">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteRule(rule.id)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );

  const renderMethodsTab = () => (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        合并方法说明
      </Typography>
      <Grid container spacing={3}>
        {Object.entries(consolidationMethods).map(([key, method]) => (
          <Grid item xs={12} md={4} key={key}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  {method.icon}
                  <Typography variant="h6" sx={{ ml: 1 }}>
                    {method.name}
                  </Typography>
                </Box>
                <Typography variant="body2" color="textSecondary" paragraph>
                  {method.description}
                </Typography>
                <Typography variant="subtitle2" gutterBottom>
                  适用范围:
                </Typography>
                <List dense>
                  {method.applicableFor.map((item, index) => (
                    <ListItem key={index} sx={{ py: 0 }}>
                      <ListItemText primary={item} />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );

  return (
    <Box sx={{ width: '100%' }}>
      <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
        <Tab label="规则配置" />
        <Tab label="合并方法" />
        <Tab label="系统设置" />
      </Tabs>

      {tabValue === 0 && renderRulesTab()}
      {tabValue === 1 && renderMethodsTab()}
      {tabValue === 2 && (
        <Box sx={{ p: 3 }}>
          <Alert severity="info">
            系统设置功能正在开发中...
          </Alert>
        </Box>
      )}

      {/* 规则配置对话框 */}
      <RuleConfigDialog
        open={ruleDialogOpen}
        onClose={() => {
          setRuleDialogOpen(false);
          setSelectedRule(null);
        }}
        rule={selectedRule}
        onSave={handleSaveRule}
      />

      {/* 规则测试对话框 */}
      <RuleTestDialog
        open={testDialogOpen}
        onClose={() => {
          setTestDialogOpen(false);
          setSelectedRule(null);
        }}
        rule={selectedRule}
      />
    </Box>
  );
};

export default MergeRulesConfig;
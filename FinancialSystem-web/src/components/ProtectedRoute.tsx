import React from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

interface ProtectedRouteProps {
  adminOnly?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ adminOnly = false }) => {
  const { isAuthenticated, user } = useAuth();

  // 如果未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/authentication/sign-in" replace />;
  }

  // 如果需要管理员权限但用户不是管理员
  if (adminOnly && user?.role !== 'ROLE_ADMIN') {
    return <Navigate to="/debt-management/Overdue-statistics" replace />;
  }

  // 渲染子路由
  return <Outlet />;
};

export default ProtectedRoute;
/**
 * 简单的树形组件
 * 替代@mui/x-tree-view，避免版本兼容性问题
 */

import React, { useState } from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Typography,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  ChevronRight as ChevronRightIcon,
  Business as BusinessIcon,
  AccountBalance as GroupIcon,
  Store as CompanyIcon,
} from '@mui/icons-material';

const SimpleTreeView = ({ data = [], onSelect, defaultExpanded = [], renderNode }) => {
  const [expanded, setExpanded] = useState(new Set(defaultExpanded));

  const handleToggle = nodeId => {
    const newExpanded = new Set(expanded);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpanded(newExpanded);
  };

  const getOrgIcon = type => {
    switch (type) {
    case 'GROUP':
      return <GroupIcon fontSize="small" color="primary" />;
    case 'COMPANY':
      return <BusinessIcon fontSize="small" color="secondary" />;
    case 'SUBSIDIARY':
      return <CompanyIcon fontSize="small" color="info" />;
    default:
      return <BusinessIcon fontSize="small" />;
    }
  };

  const renderTreeNode = (node, level = 0) => {
    const hasChildren = node.children && node.children.length > 0;
    const isExpanded = expanded.has(node.id);

    return (
      <React.Fragment key={node.id}>
        <ListItem
          disablePadding
          sx={{
            pl: level * 2,
            borderLeft: level > 0 ? '1px solid #e0e0e0' : 'none',
            marginLeft: level > 0 ? '12px' : 0,
          }}
        >
          <ListItemButton
            onClick={() => {
              if (hasChildren) {
                handleToggle(node.id);
              }
              if (onSelect) {
                onSelect(node);
              }
            }}
            sx={{
              py: 0.5,
              '&:hover': {
                backgroundColor: 'rgba(25, 118, 210, 0.04)',
              },
            }}
          >
            <ListItemIcon sx={{ minWidth: 32 }}>
              {hasChildren ? (
                isExpanded ? (
                  <ExpandMoreIcon />
                ) : (
                  <ChevronRightIcon />
                )
              ) : (
                <Box sx={{ width: 24 }} />
              )}
            </ListItemIcon>

            <ListItemIcon sx={{ minWidth: 32 }}>{getOrgIcon(node.type)}</ListItemIcon>

            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2">{node.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    ({node.code})
                  </Typography>
                </Box>
              }
              sx={{ my: 0 }}
            />

            {/* 渲染自定义内容 */}
            {renderNode && renderNode(node)}
          </ListItemButton>
        </ListItem>

        {/* 渲染子节点 */}
        {hasChildren && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {node.children.map(child => renderTreeNode(child, level + 1))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  return (
    <List component="nav" aria-labelledby="nested-list-subheader">
      {data.map(node => renderTreeNode(node, 0))}
    </List>
  );
};

export default SimpleTreeView;

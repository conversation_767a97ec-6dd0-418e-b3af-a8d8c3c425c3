import React from 'react';
import { createRoot } from 'react-dom/client';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import App from 'App';
import { MaterialUIControllerProvider } from 'context';
import { AuthProvider } from 'context/AuthContext.js';
import ErrorBoundary from 'components/ErrorBoundary.js';
// Import console cleanup for production
import 'utils/console-cleanup.js';

/**
=========================================================
* Material Dashboard 2 React - v2.2.0
=========================================================

* Product Page: https://www.creative-tim.com/product/material-dashboard-react
* Copyright 2023 Creative Tim (https://www.creative-tim.com)

Coded by www.creative-tim.com

=========================================================

* The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
*/

// 确保 fetch API 可用
if (typeof window.fetch === 'undefined') {
  console.warn('Fetch API is not available, using polyfill');
  // 如果 fetch 不可用，尝试使用 polyfill
  try {
    // 尝试从 CDN 加载 fetch polyfill
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/whatwg-fetch@3.6.2/dist/fetch.umd.js';
    script.async = false;
    document.head.appendChild(script);

    // 等待脚本加载完成
    script.onload = () => {
      console.log('Fetch polyfill loaded successfully');
      initializeApp();
    };

    script.onerror = () => {
      console.error('Failed to load fetch polyfill');
      alert('应用程序加载失败，请使用现代浏览器访问');
    };
  } catch (error) {
    console.error('Error loading fetch polyfill:', error);
    alert('应用程序加载失败，请使用现代浏览器访问');
  }
} else {
  console.log('Native fetch API is available');
  initializeApp();
}

function initializeApp() {
  const container = document.getElementById('app');
  if (container) {
    const root = createRoot(container);
    console.log('container:', container);

    // 恢复原始的应用渲染
    console.log('渲染完整的财务管理系统应用...');
    root.render(
      <ErrorBoundary>
        <BrowserRouter>
          <MaterialUIControllerProvider>
            <AuthProvider>
              <App />
            </AuthProvider>
          </MaterialUIControllerProvider>
        </BrowserRouter>
      </ErrorBoundary>,
    );
  } else {
    console.error('Container element not found');
  }
}
/* 强制刷新缓存 - Mon Aug 18 11:18:17 CST 2025 */
